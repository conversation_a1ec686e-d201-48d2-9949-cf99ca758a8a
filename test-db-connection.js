require('dotenv').config({ path: './server/.env' });
const mongoose = require('mongoose');

console.log('🔗 MongoDB URL configured:', process.env.MONGO_URL ? 'Yes' : 'No');
console.log('📡 Attempting to connect to database...');

mongoose.connect(process.env.MONGO_URL, { 
    serverSelectionTimeoutMS: 10000,
    bufferCommands: false
}).then(() => {
    console.log('✅ Database connected successfully!');
    console.log('🎉 MongoDB Atlas connection is working');
    mongoose.disconnect();
    process.exit(0);
}).catch(err => {
    console.error('❌ Database connection failed:');
    console.error('   Error:', err.message);
    console.error('💡 Check your .env file and internet connection');
    process.exit(1);
});
